from library import *
class TTC:
    def __init__(self, username, password) -> None:
        self.username, self.password = username, password
        self.proxyDict               = ''
        self.API_URL                 = 'https://tuongtaccheo.com/'
        
    def addProxy(self, proxy: str):
        try:
            if len(proxy.split(':')) >= 4:
                parts = proxy.split(':')
                iport = ":".join(parts[:2])
                userpass = ":".join(parts[2:])
                self.proxyDict = {
                    'https': 'http://{}@{}'.format(userpass,iport),
                    'http': 'http://{}@{}'.format(userpass,userpass)
                    }
            elif len(proxy.split(':')) >= 2:
                proxy_url = 'http://{}'.format(proxy)
                self.proxyDict   = { 
                "http"  : proxy_url, 
                "https" : proxy_url
            }
            else:
                self.proxyDict   = ''

            ip = requests.get('https://www.myip.com/', proxies=self.proxyDict).text
            # print('My ip address:',ip.split('<span id="ip">')[1].split('</span>')[0],self.username,end='\r')
            return True
        except: return False
    def __requests(self, endpoint: str, data = None):
        for x in range(3):
            try:
                full_url = self.API_URL + endpoint
                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                    f.write(f"[{datetime.now()}] Attempting request to: {full_url}\n")

                if data  == None:self.lastReponse = requests.get(full_url, headers = self.headers, proxies = self.proxyDict, timeout = 15)
                else:self.lastReponse = requests.post(full_url, data = data, headers = self.headers, timeout = 15)

                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                    f.write(f"[{datetime.now()}] Request successful, status: {self.lastReponse.status_code}\n")
                return True

            except Exception as e:
                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                    f.write(f"[{datetime.now()}] Request failed, attempt {x+1}/3, error: {e}\n")
            time.sleep(1)

        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
            f.write(f"[{datetime.now()}] All request attempts failed for: {self.API_URL + endpoint}\n")
        return False  # Return False if all attempts failed
    
    def loginTTC(self):
        try:
            rsp           = requests.get('https://tuongtaccheo.com/', proxies=self.proxyDict)
            cookie        =  rsp.headers['Set-cookie']
            self.headers  = {
                'Content-type': "application/x-www-form-urlencoded",
                "x-requested-with":"XMLHttpRequest",
                "Cookie":cookie,
            }
            if self.__requests('login.php', data={'username': self.username,'password': self.password, 'submit': 'ĐĂNG NHẬP',}):
                if self.__requests('home.php'):
                    if 'soduchinh' in self.lastReponse.text:
                        return {'status': "success", 'data': {'user': self.username, 'xu': self.lastReponse.text.split('id="soduchinh">')[1].split("</strong>")[0]}}
                    else:
                        return {'status': "error", 'mess': "Sai tài khoản hoặc mật khẩu"}
        except:pass
        return {'status': "error", 'mess': "Sai tài khoản hoặc mật khẩu"}
    
    def getXuTTC(self):
        try:
            if self.__requests('home.php'):
                return {'user': self.username, 'xu': self.lastReponse.text.split('id="soduchinh">')[1].split("</strong>")[0]}
        except: return {'user':self.username, 'xu': 0}
        
    def getMaLucTTC(self):
        if self.__requests('caidat'):
            soup = BeautifulSoup(self.lastReponse.text, 'html.parser')
            self.username = soup.find('tr', class_='text-center').find('td').text
            self.xuTTC = soup.find_all('tr', class_='text-center')[1].find('td').text
            self.maluc = soup.find_all('tr', class_='text-center')[2].find('td').text
            if 'soduchinh' in self.lastReponse.text:
                return {'user': self.username, 'xu': self.xuTTC, 'maluc': self.maluc}
            else: return False
        return False
        
    def datNik(self, username: str):
        try:
            if self.__requests('cauhinh/addtiktok.php?link={}&nickchay={}'.format(username,username)):
                if '1' in self.lastReponse.text:
                    return {'status': "success", 'mess': "Đặt nick thành công"}
                time.sleep(3)
            return {'status': "error", 'mess': self.lastReponse.text}
        except: return {'status': 'error', 'mess': 'Có lỗi xảy ra khi đặt nick !'}
        
    def getJob(self,job_type):
        try:
            # Debug info will be written to file instead of print
            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                f.write(f"[{datetime.now()}] TTC getJob called for type: {job_type}\n")
                f.write(f"[{datetime.now()}] TTC headers: {getattr(self, 'headers', 'NOT SET')}\n")
            if job_type == 'love':
                if self.__requests('tiktok/kiemtien/getpost.php'):
                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                        f.write(f"[{datetime.now()}] TTC {job_type} response: {self.lastReponse.text[:200]}...\n")
                    if '[]' in self.lastReponse.text:
                        return 0

                    # Check if response contains rate limit or error info
                    try:
                        response_json = self.lastReponse.json()
                        if isinstance(response_json, dict) and 'error' in response_json:
                            error_msg = str(response_json.get('error', ''))
                            if 'chậm' in error_msg.lower() or 'limit' in error_msg.lower() or 'countdown' in response_json:
                                if 'countdown' not in response_json:
                                    response_json['countdown'] = 60
                                return response_json
                        return response_json
                    except:
                        return self.lastReponse.json()
                else:
                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                        f.write(f"[{datetime.now()}] TTC {job_type} request failed\n")
                    return 0  # Request failed, no jobs available
            elif job_type == 'follow':
                if self.__requests('tiktok/kiemtien/subcheo/getpost.php'):
                    print(f"[DEBUG] TTC {type} response: {self.lastReponse.text[:200]}...")  # Debug log
                    if '[]' in self.lastReponse.text:
                        return 0

                    # Check if response contains rate limit or error info
                    try:
                        response_json = self.lastReponse.json()
                        if isinstance(response_json, dict) and 'error' in response_json:
                            error_msg = str(response_json.get('error', ''))
                            if 'chậm' in error_msg.lower() or 'limit' in error_msg.lower() or 'countdown' in response_json:
                                if 'countdown' not in response_json:
                                    response_json['countdown'] = 60
                                return response_json
                        return response_json
                    except:
                        return self.lastReponse.json()
                else:
                    print(f"[DEBUG] TTC {type} request failed")  # Debug log
                    return 0  # Request failed, no jobs available
            elif job_type == 'cmt' or job_type == 'comment':
                if self.__requests('tiktok/kiemtien/cmtcheo/getpost.php'):
                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                        f.write(f"[{datetime.now()}] TTC {type} response status: {self.lastReponse.status_code}\n")
                        f.write(f"[{datetime.now()}] TTC {type} response text: {self.lastReponse.text[:500]}...\n")

                    if '[]' in self.lastReponse.text:
                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                            f.write(f"[{datetime.now()}] TTC {type} - Empty jobs array detected\n")
                        return 0

                    # Check if response contains rate limit or error info
                    try:
                        response_json = self.lastReponse.json()
                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                            f.write(f"[{datetime.now()}] TTC {type} parsed JSON type: {str(type(response_json))}\n")

                        if isinstance(response_json, dict) and 'error' in response_json:
                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                f.write(f"[{datetime.now()}] TTC {type} - Error response detected: {response_json}\n")
                            # Check for rate limit messages
                            error_msg = str(response_json.get('error', ''))
                            if 'chậm' in error_msg.lower() or 'limit' in error_msg.lower() or 'countdown' in response_json:
                                if 'countdown' not in response_json:
                                    response_json['countdown'] = 60  # Default countdown
                                return response_json

                        if isinstance(response_json, list):
                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                f.write(f"[{datetime.now()}] TTC {type} - Jobs list detected, length: {len(response_json)}\n")

                        return response_json
                    except Exception as e:
                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                            f.write(f"[{datetime.now()}] TTC {type} - JSON parse error: {e}\n")
                        return self.lastReponse.json()
                else:
                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                        f.write(f"[{datetime.now()}] TTC {type} request failed - no response\n")
                    return 0  # Request failed, no jobs available
            else:
                return 0  # Unknown job type

        except Exception as e:
            logging.basicConfig(filename=errorlog, level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')
            logging.exception("Exception occurred")
            # Check if response contains rate limit info
            try:
                if hasattr(self, 'lastReponse') and self.lastReponse:
                    response_json = self.lastReponse.json()
                    if isinstance(response_json, dict):
                        # Check for common rate limit patterns
                        if 'countdown' in response_json or 'wait' in str(response_json).lower():
                            return response_json
                        if 'error' in response_json and ('chậm' in str(response_json) or 'limit' in str(response_json).lower()):
                            # Add default countdown if not provided
                            if 'countdown' not in response_json:
                                response_json['countdown'] = 60
                            return response_json
            except:
                pass
            return {'status': 'error', 'mess': 'Có lỗi xảy ra khi lấy nhiệm vụ!'}
  
    def getXuJob(self, job_type: str, id: str):
        try:
            if job_type == 'follow':
                if self.__requests('tiktok/kiemtien/subcheo/nhantien.php', data={'id': id}):
                    print(self.lastReponse.json(),self.username,id)
                    if 'mess' in self.lastReponse.text and "cộng" in self.lastReponse.text:
                        return {'status': 'success', 'mess': str(self.lastReponse.json()['mess']).split('cộng ')[1].split(' xu')[0]}
                    else:
                        if 'error2' in self.lastReponse.text:
                            return {'status': 'error', 'mess': self.lastReponse.json()['error2']}
                        if 'error' in self.lastReponse.text:

                            return {'status': 'error', 'mess': self.lastReponse.json()['error']}
                        else:
                            return {'status': 'error', 'mess': self.lastReponse.json()['mess']}
            elif job_type == 'love':
                if self.__requests('tiktok/kiemtien/nhantien.php', data={'id': id}):
                    print(self.lastReponse.json(),self.username)
                    if 'mess' in self.lastReponse.text and "cộng" in self.lastReponse.text:
                        return {'status': 'success', 'mess': str(self.lastReponse.json()['mess']).split('cộng ')[1].split(' xu')[0]}
                    else:
                        if 'error2' in self.lastReponse.text:
                            return {'status': 'error', 'mess': self.lastReponse.json()['error2']}
                        if 'error' in self.lastReponse.text:

                            return {'status': 'error', 'mess': self.lastReponse.json()['error']}
                        else:
                            return {'status': 'error', 'mess': self.lastReponse.json()['mess']}
            elif job_type == 'cmt' or job_type == 'comment':
                if self.__requests('tiktok/kiemtien/cmtcheo/nhantien.php', data={'id': id}):
                    print(self.lastReponse.json(),self.username,id)
                    if 'mess' in self.lastReponse.text and "cộng" in self.lastReponse.text:
                        return {'status': 'success', 'mess': str(self.lastReponse.json()['mess']).split('cộng ')[1].split(' xu')[0]}
                    else:
                        if 'error2' in self.lastReponse.text:
                            return {'status': 'error', 'mess': self.lastReponse.json()['error2']}
                        if 'error' in self.lastReponse.text:

                            return {'status': 'error', 'mess': self.lastReponse.json()['error']}
                        else:
                            return {'status': 'error', 'mess': self.lastReponse.json()['mess']}
            elif type == 'cmt':
                if self.__requests('tiktok/kiemtien/cmtcheo/nhantien.php', data={'id': id}):
                    print(self.lastReponse.json(),self.username,id)
                    if 'mess' in self.lastReponse.text and "cộng" in self.lastReponse.text:
                        return {'status': 'success', 'mess': str(self.lastReponse.json()['mess']).split('cộng ')[1].split(' xu')[0]}
                    else:
                        if 'error2' in self.lastReponse.text:
                            return {'status': 'error', 'mess': self.lastReponse.json()['error2']}
                        if 'error' in self.lastReponse.text:

                            return {'status': 'error', 'mess': self.lastReponse.json()['error']}
                        else:
                            return {'status': 'error', 'mess': self.lastReponse.json()['mess']}

        except Exception as e:
            logging.basicConfig(filename=errorlog, level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')
            logging.exception("Exception occurred")
            return {'status': 'error', 'mess': 'Có lỗi xảy ra khi nhận xu!'}

    def chuyenXu(self, usernhan, sluong):
        try:
            data = {
                'usernhan': usernhan,
                'passnicktang': self.password,
                'sluong': sluong,
                'loai': 'xu',
            }

            response = requests.post('https://tuongtaccheo.com/caidat/tangxu.php', headers=self.headers, data=data).text
            if '4' in response:
                xuconlai = self.getXuTTC()['xu']
                return {'status': 'success', 'mess': 'chuyển xu thành công.','xuconlai':xuconlai}
            else: return {'status': 'error', 'mess': 'chuyển xu thất bại.'}
        except: return {'status': 'error', 'mess': 'chuyển xu thất bại.'}