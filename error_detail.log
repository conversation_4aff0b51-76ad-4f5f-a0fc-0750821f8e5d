﻿[ERROR] 2025-06-08 14:25:54.861200 | Index: 1 | Status: <PERSON><PERSON><PERSON> <PERSON>hi<PERSON> vụ sau 1 gi<PERSON>y
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:25:55.863663 | Index: 1 | Status: L<PERSON>y nhiệm vụ sau 1 giây
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:25:56.865646 | Index: 1 | Status: <PERSON><PERSON><PERSON> nhi<PERSON> vụ sau 1 gi<PERSON><PERSON>
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:25:57.868269 | Index: 1 | Status: Lấy nhiệm vụ sau 1 giây
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:25:58.869938 | Index: 1 | Status: Lấy nhiệm vụ sau 1 giây
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:25:59.872195 | Index: 1 | Status: Lấy nhiệm vụ sau 1 giây
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:00.873693 | Index: 1 | Status: Lấy nhiệm vụ sau 1 giây
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:01.875642 | Index: 1 | Status: Lấy nhiệm vụ sau 1 giây
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:02.877692 | Index: 1 | Status: Lấy nhiệm vụ sau 1 giây
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:03.879666 | Index: 1 | Status: Lấy nhiệm vụ sau 1 giây
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:04.881637 | Index: 1 | Status: Lấy nhiệm vụ sau 1 giây
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:05.883576 | Index: 1 | Status: Lấy nhiệm vụ sau 1 giây
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:06.885667 | Index: 1 | Status: Lấy nhiệm vụ sau 1 giây
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:07.887657 | Index: 1 | Status: Lấy nhiệm vụ sau 1 giây
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:08.890604 | Index: 1 | Status: Lấy nhiệm vụ sau 1 giây
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:10.938147 | Index: 1 | Status: Kết nối internet ổn định.
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:11.947982 | Index: 1 | Status: Kết nối internet ổn định.
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:12.950325 | Index: 1 | Status: Kết nối internet ổn định.
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:13.952342 | Index: 1 | Status: Kết nối internet ổn định.
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:14.954248 | Index: 1 | Status: Kết nối internet ổn định.
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:15.956400 | Index: 1 | Status: Kết nối internet ổn định.
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:16.958360 | Index: 1 | Status: Kết nối internet ổn định.
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:17.959931 | Index: 1 | Status: Kết nối internet ổn định.
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:18.961222 | Index: 1 | Status: Kết nối internet ổn định.
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:19.963387 | Index: 1 | Status: Kết nối internet ổn định.
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:20.970843 | Index: 1 | Status: Kết nối internet ổn định.
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:21.972878 | Index: 1 | Status: Kết nối internet ổn định.
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:22.974906 | Index: 1 | Status: Kết nối internet ổn định.
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:23.976920 | Index: 1 | Status: Kết nối internet ổn định.
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

[ERROR] 2025-06-08 14:26:24.978880 | Index: 1 | Status: Kết nối internet ổn định.
Traceback (most recent call last):
  File "d:\rieng tu\TOOL\AutoChrome-đã sửa login\AutoChrome-SourceBan\src\modules\autoChrome.py", line 1032, in __earnMoneyTTC
    if str(job['link']) in str(self.__jobDie):
           ~~~^^^^^^^^
TypeError: string indices must be integers, not 'str'

