[2025-07-14 01:54:13.069649] Attempting request to: https://tuongtaccheo.com/login.php
[2025-07-14 01:54:13.333622] Request successful, status: 200
[2025-07-14 01:54:13.334120] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 01:54:13.465366] Request successful, status: 200
[2025-07-14 01:54:15.362203] Attempting request to: https://tuongtaccheo.com/login.php
[2025-07-14 01:54:15.626691] Request successful, status: 200
[2025-07-14 01:54:15.627189] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 01:54:15.762927] Request successful, status: 200
[2025-07-14 01:54:28.980409] Attempting request to: https://tuongtaccheo.com/cauhinh/addtiktok.php?link=g.hoang.5n.2006&nickchay=g.hoang.5n.2006
[2025-07-14 01:54:30.009422] Request successful, status: 200
[2025-07-14 01:54:30.646714] Attempting request to: https://tuongtaccheo.com/cauhinh/addtiktok.php?link=ta.chinh.4m.2002&nickchay=ta.chinh.4m.2002
[2025-07-14 01:54:31.588009] Request successful, status: 200
[2025-07-14 01:54:35.013130] TTC getJob called for type: comment
[2025-07-14 01:54:35.013130] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=pcv1kfirj8cun66tl6nu39l8l2; Path=/'}
[2025-07-14 01:54:35.014129] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 01:54:35.142879] Request successful, status: 200
[2025-07-14 01:54:35.143379] TTC comment response status: 200
[2025-07-14 01:54:35.143379] TTC comment response text: {"error":"Lấy nhiệm vụ sau 2 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!","countdown":2}...
[2025-07-14 01:54:35.143877] TTC comment parsed JSON type: <class 'dict'>
[2025-07-14 01:54:35.144375] TTC comment - Error response detected: {'error': 'Lấy nhiệm vụ sau 2 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!', 'countdown': 2}
[2025-07-14 01:54:36.591660] TTC getJob called for type: comment
[2025-07-14 01:54:36.591660] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=ecbdbj50ohb189cuifskr5qdm5; Path=/'}
[2025-07-14 01:54:36.592662] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 01:54:36.741871] Request successful, status: 200
[2025-07-14 01:54:36.742371] TTC comment response status: 200
[2025-07-14 01:54:36.742371] TTC comment response text: {"error":"Lấy nhiệm vụ sau 13 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!","countdown":13}...
[2025-07-14 01:54:36.742869] TTC comment parsed JSON type: <class 'dict'>
[2025-07-14 01:54:36.743368] TTC comment - Error response detected: {'error': 'Lấy nhiệm vụ sau 13 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!', 'countdown': 13}
[2025-07-14 01:54:37.146590] TTC getJob called for type: comment
[2025-07-14 01:54:37.146590] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=pcv1kfirj8cun66tl6nu39l8l2; Path=/'}
[2025-07-14 01:54:37.147102] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 01:54:37.296802] Request successful, status: 200
[2025-07-14 01:54:37.297306] TTC comment response status: 200
[2025-07-14 01:54:37.297306] TTC comment response text: [{"idpost":"7526058282377563399","link":"https:\/\/www.tiktok.com\/@khongnho743\/video\/7526058282377563399","nd":"[\"\\ud83d\\ude02\\ud83d\\ude02 Ch\\u1ea5t\",\"nh\\u1ea1c n\\u00e0y thu\\u1ed9c th\\u1ec3 lo\\u1ea1i n\\u00e0o \\u1ea1=))))\",\"Ch\\u1ea5ttt\\ud83d\\ude01\",\"\\u2764\\u2764\\u2764\",\"ch\\u00e0o anh\",\"c\\u0169ng \\u0111c m\\u00e0\",\"\\u0110\\u1eb9p trai nha\"]"},{"idpost":"7526571228119698695","link":"https:\/\/www.tiktok.com\/@nguyennthihongtham2804\/video\/7526571228119698695"...
[2025-07-14 01:54:37.298298] TTC comment parsed JSON type: <class 'list'>
[2025-07-14 01:54:37.298798] TTC comment - Jobs list detected, length: 36
[2025-07-14 01:54:37.334229] TTC starting jobs iteration: 36 jobs
[2025-07-14 01:54:37.334731] TTC comment settings: max=2, cache=2
[2025-07-14 01:54:37.335210] TTC processing job 1: 7526058282377563399, https://www.tiktok.com/@khongnho743/video/7526058282377563399
[2025-07-14 01:54:37.335750] TTC comment content found: ["\ud83d\ude02\ud83d\ude02 Ch\u1ea5t","nh\u1ea1c n\u00e0y thu\u1ed9c th\u1ec3 lo\u1ea1i n\u00e0o \u1...
[2025-07-14 01:54:37.337208] TTC navigating to: https://www.tiktok.com/@khongnho743/video/7526058282377563399
[2025-07-14 01:54:41.143369] TTC navigation successful
[2025-07-14 01:54:41.143868] TTC calling __clickXmlJobs with comment: True
[2025-07-14 01:54:49.750313] TTC getJob called for type: comment
[2025-07-14 01:54:49.750313] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=ecbdbj50ohb189cuifskr5qdm5; Path=/'}
[2025-07-14 01:54:49.750802] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 01:54:49.897521] Request successful, status: 200
[2025-07-14 01:54:49.898019] TTC comment response status: 200
[2025-07-14 01:54:49.898019] TTC comment response text: [{"idpost":"7526110316803935506","link":"https:\/\/www.tiktok.com\/@buiyennhireview\/video\/7526110316803935506","nd":"[\".\",\"\",\"\",\"\",\"Ch\\u1ecb \\u01a1i, lo\\u1ea1i n\\u00e0y d\\u00f9ng cho body \\u0111c h\\u00f4ng \\u1ea1 hay ch\\u1ec9 x\\u1ecbt m\\u1eb7t?\",\"T\\u1edb \\u0111\\u1ec3 trong c\\u1eb7p, x\\u1ecbt l\\u1ea1i sau m\\u1ed7i 3 ti\\u1ebfng l\\u00e0 y\\u00ean t\\u00e2m.\",\"\",\"\",\"\",\"\",\"Gi\\u1eefa ng\\u00e0y x\\u1ecbt l\\u1ea1i m\\u00e0 kh\\u00f4ng th\\u1ea5y b\\u00ed b\\...
[2025-07-14 01:54:49.899517] TTC comment parsed JSON type: <class 'list'>
[2025-07-14 01:54:49.900017] TTC comment - Jobs list detected, length: 36
[2025-07-14 01:54:49.937444] TTC starting jobs iteration: 36 jobs
[2025-07-14 01:54:49.937943] TTC comment settings: max=2, cache=2
[2025-07-14 01:54:49.938928] TTC processing job 1: 7526110316803935506, https://www.tiktok.com/@buiyennhireview/video/7526110316803935506
[2025-07-14 01:54:49.939427] TTC comment content found: [".","","","","Ch\u1ecb \u01a1i, lo\u1ea1i n\u00e0y d\u00f9ng cho body \u0111c h\u00f4ng \u1ea1 hay ...
[2025-07-14 01:54:49.943919] TTC navigating to: https://www.tiktok.com/@buiyennhireview/video/7526110316803935506
[2025-07-14 01:54:52.923679] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 01:54:52.946152] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 01:54:54.480678] TTC navigation successful
[2025-07-14 01:54:54.481177] TTC calling __clickXmlJobs with comment: True
[2025-07-14 01:55:02.016447] TTC comment input clicked successfully
[2025-07-14 01:55:03.084872] TTC typing comment: 😂😂 Chất...
[2025-07-14 01:55:03.085386] TTC selector div[data-e2e="comment-input"] div[contenteditable="true"] failed: name 'comment_copied' is not defined
[2025-07-14 01:55:03.085886] TTC trying selector: div[data-e2e="comment-input"] div[role="textbox"]
[2025-07-14 01:55:03.107842] TTC found comment input with selector: div[data-e2e="comment-input"] div[role="textbox"]
[2025-07-14 01:55:04.655871] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 01:55:04.675832] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 01:55:12.181129] TTC comment input clicked successfully
[2025-07-14 01:55:13.237805] TTC typing comment: 😂😂 Chất...
[2025-07-14 01:55:13.238786] TTC selector div[data-e2e="comment-input"] div[role="textbox"] failed: name 'comment_copied' is not defined
[2025-07-14 01:55:13.238786] TTC trying selector: div[role="textbox"][contenteditable="true"]
[2025-07-14 01:55:13.262757] TTC found comment input with selector: div[role="textbox"][contenteditable="true"]
[2025-07-14 01:55:13.805198] TTC comment input clicked successfully
[2025-07-14 01:55:14.881864] TTC typing comment: ...
[2025-07-14 01:55:14.882674] TTC selector div[data-e2e="comment-input"] div[contenteditable="true"] failed: name 'comment_copied' is not defined
[2025-07-14 01:55:14.883172] TTC trying selector: div[data-e2e="comment-input"] div[role="textbox"]
[2025-07-14 01:55:14.909606] TTC found comment input with selector: div[data-e2e="comment-input"] div[role="textbox"]
[2025-07-14 01:55:22.369337] TTC comment input clicked successfully
[2025-07-14 01:55:23.435180] TTC typing comment: 😂😂 Chất...
[2025-07-14 01:55:23.435688] TTC selector div[role="textbox"][contenteditable="true"] failed: name 'comment_copied' is not defined
[2025-07-14 01:55:23.436191] TTC trying selector: div[contenteditable="true"][data-placeholder*="comment"]
[2025-07-14 01:55:24.011671] TTC comment input clicked successfully
[2025-07-14 01:55:24.485396] TTC trying selector: div[contenteditable="true"][data-placeholder*="Comment"]
[2025-07-14 01:55:25.061790] TTC typing comment: ...
[2025-07-14 01:55:25.062788] TTC selector div[data-e2e="comment-input"] div[role="textbox"] failed: name 'comment_copied' is not defined
[2025-07-14 01:55:25.063270] TTC trying selector: div[role="textbox"][contenteditable="true"]
[2025-07-14 01:55:25.084750] TTC found comment input with selector: div[role="textbox"][contenteditable="true"]
[2025-07-14 01:55:25.535380] TTC trying selector: div[contenteditable="true"][placeholder*="comment"]
[2025-07-14 01:55:26.583851] TTC trying selector: div[contenteditable="true"][placeholder*="Comment"]
[2025-07-14 01:55:27.632559] TTC trying selector: div[contenteditable="true"]
[2025-07-14 01:55:27.654017] TTC found comment input with selector: div[contenteditable="true"]
[2025-07-14 01:55:34.052074] TTC comment input clicked successfully
[2025-07-14 01:55:35.105457] TTC typing comment: ...
[2025-07-14 01:55:35.105957] TTC selector div[role="textbox"][contenteditable="true"] failed: name 'comment_copied' is not defined
[2025-07-14 01:55:35.106431] TTC trying selector: div[contenteditable="true"][data-placeholder*="comment"]
[2025-07-14 01:55:36.142133] TTC trying selector: div[contenteditable="true"][data-placeholder*="Comment"]
[2025-07-14 01:55:36.708426] TTC comment input clicked successfully
[2025-07-14 01:55:37.181654] TTC trying selector: div[contenteditable="true"][placeholder*="comment"]
[2025-07-14 01:55:37.756396] TTC typing comment: 😂😂 Chất...
[2025-07-14 01:55:37.756895] TTC selector div[contenteditable="true"] failed: name 'comment_copied' is not defined
[2025-07-14 01:55:37.757394] TTC trying selector: [contenteditable="true"]
[2025-07-14 01:55:37.778354] TTC found comment input with selector: [contenteditable="true"]
[2025-07-14 01:55:38.219945] TTC trying selector: div[contenteditable="true"][placeholder*="Comment"]
[2025-07-14 01:55:39.263472] TTC trying selector: div[contenteditable="true"]
[2025-07-14 01:55:39.282934] TTC found comment input with selector: div[contenteditable="true"]
[2025-07-14 01:55:46.850597] TTC comment input clicked successfully
[2025-07-14 01:55:47.902580] TTC typing comment: 😂😂 Chất...
[2025-07-14 01:55:47.903085] TTC selector [contenteditable="true"] failed: name 'comment_copied' is not defined
[2025-07-14 01:55:47.903710] TTC trying selector: div[data-e2e="comment-input"]
[2025-07-14 01:55:47.926017] TTC found comment input with selector: div[data-e2e="comment-input"]
[2025-07-14 01:55:48.240914] TTC comment input clicked successfully
[2025-07-14 01:55:49.290421] TTC typing comment: ...
[2025-07-14 01:55:49.291078] TTC selector div[contenteditable="true"] failed: name 'comment_copied' is not defined
[2025-07-14 01:55:49.291436] TTC trying selector: [contenteditable="true"]
[2025-07-14 01:55:49.310887] TTC found comment input with selector: [contenteditable="true"]
[2025-07-14 01:55:55.898088] TTC found child input: div[contenteditable="true"]
[2025-07-14 01:55:57.041096] TTC comment input clicked successfully
[2025-07-14 01:55:58.103809] TTC typing comment: 😂😂 Chất...
[2025-07-14 01:55:58.104806] TTC selector div[data-e2e="comment-input"] failed: name 'comment_copied' is not defined
[2025-07-14 01:55:58.105306] TTC trying selector: textarea[placeholder*="comment"]
[2025-07-14 01:55:58.335665] TTC comment input clicked successfully
[2025-07-14 01:55:59.157578] TTC trying selector: textarea[placeholder*="Comment"]
[2025-07-14 01:55:59.398750] TTC typing comment: ...
[2025-07-14 01:55:59.399247] TTC selector [contenteditable="true"] failed: name 'comment_copied' is not defined
[2025-07-14 01:55:59.399745] TTC trying selector: div[data-e2e="comment-input"]
[2025-07-14 01:55:59.424182] TTC found comment input with selector: div[data-e2e="comment-input"]
[2025-07-14 01:56:00.210535] TTC trying selector: input[placeholder*="comment"]
[2025-07-14 01:56:01.262125] TTC trying selector: input[placeholder*="Comment"]
[2025-07-14 01:56:07.334474] TTC found child input: div[contenteditable="true"]
[2025-07-14 01:56:08.450339] TTC comment input clicked successfully
[2025-07-14 01:56:09.504026] TTC typing comment: ...
[2025-07-14 01:56:09.504526] TTC selector div[data-e2e="comment-input"] failed: name 'comment_copied' is not defined
[2025-07-14 01:56:09.505004] TTC trying selector: textarea[placeholder*="comment"]
[2025-07-14 01:56:10.546447] TTC trying selector: textarea[placeholder*="Comment"]
[2025-07-14 01:56:11.589192] TTC trying selector: input[placeholder*="comment"]
[2025-07-14 01:56:12.632032] TTC trying selector: input[placeholder*="Comment"]
